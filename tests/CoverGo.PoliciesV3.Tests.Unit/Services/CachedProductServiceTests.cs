using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.Products.Client;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for CachedProductService to verify service behavior and method delegation.
/// Note: Full caching behavior testing requires integration tests due to CacheProvider being a concrete class.
/// These tests focus on the service logic, tenant validation, and method delegation patterns.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "Product")]
public class CachedProductServiceTests : IDisposable
{
    private readonly Mock<ITenantProvider> _mockTenantProvider;
    private readonly Mock<IProductService> _mockInnerService;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public CachedProductServiceTests()
    {
        _mockTenantProvider = new Mock<ITenantProvider>();
        _mockInnerService = new Mock<IProductService>();
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Test Helpers

    /// <summary>
    /// Creates a CachedProductService instance for testing.
    /// Note: Uses a null cache provider since CacheProvider cannot be mocked.
    /// This limits testing to non-caching behavior.
    /// </summary>
    private CachedProductService CreateServiceWithoutCache() => new(
        _mockTenantProvider.Object,
        _mockInnerService.Object,
        null!); // CacheProvider cannot be mocked, so we pass null for non-cache tests

    private ProductId CreateProductId() => new()
    {
        Plan = _fixture.Create<string>(),
        Type = _fixture.Create<string>(),
        Version = _fixture.Create<string>()
    };

    private DomainProductId CreateDomainProductId() => new(
        _fixture.Create<string>(),
        _fixture.Create<string>(),
        _fixture.Create<string>());

    private TenantId CreateTenantId() => new(_fixture.Create<string>());

    private string CreateValidSchema() => """
        {
            "type": "object",
            "properties": {
                "name": { "type": "string", "required": true },
                "age": { "type": "number", "minimum": 0 },
                "email": { "type": "string", "format": "email" }
            }
        }
        """;

    private void SetupTenantProvider(TenantId? tenantId = null)
    {
        tenantId ??= CreateTenantId();
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = tenantId;
                return true;
            });
    }

    private void SetupTenantProviderFailure()
    {
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = null;
                return false;
            });
    }

    #endregion

    #region Service Instantiation Tests

    [Fact]
    public void CachedProductService_CanBeInstantiated()
    {
        // Arrange & Act
        CachedProductService service = CreateService();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeAssignableTo<IProductService>();
    }

    [Fact]
    public void CachedProductService_ShouldImplementIProductService()
    {
        // Arrange & Act
        CachedProductService service = CreateService();

        // Assert - Service should implement the interface correctly
        service.Should().BeAssignableTo<IProductService>();

        // Verify service has the expected methods
        Type serviceType = service.GetType();
        serviceType.GetMethod(nameof(IProductService.GetProductMemberSchema)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetProductPackageType)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetAvailablePlanIds)).Should().NotBeNull();
    }

    #endregion

    #region GetProductMemberSchema - Cache Hit Tests

    [Fact]
    public async Task GetProductMemberSchema_WithCacheHit_ShouldReturnCachedValueWithoutCallingInnerService()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string cachedSchema = CreateValidSchema();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync(cachedSchema);

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().Be(cachedSchema);

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was NOT called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);

        // Verify nothing was set in cache (since we had a hit)
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithCacheHitAndDifferentProductIds_ShouldUseDifferentCacheKeys()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId1 = CreateProductId();
        ProductId productId2 = CreateProductId();
        string cachedSchema1 = CreateValidSchema();
        string cachedSchema2 = """{"type": "object", "properties": {"company": {"type": "string"}}}""";

        string expectedCacheKey1 = GetExpectedCacheKey(tenantId, productId1);
        string expectedCacheKey2 = GetExpectedCacheKey(tenantId, productId2);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken))
            .ReturnsAsync(cachedSchema1);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken))
            .ReturnsAsync(cachedSchema2);

        CachedProductService service = CreateService();

        // Act
        string? result1 = await service.GetProductMemberSchema(productId1, _cancellationToken);
        string? result2 = await service.GetProductMemberSchema(productId2, _cancellationToken);

        // Assert
        result1.Should().Be(cachedSchema1);
        result2.Should().Be(cachedSchema2);

        // Verify different cache keys were used
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken),
            Times.Once);
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken),
            Times.Once);

        // Verify inner service was never called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    #endregion

    #region GetProductMemberSchema - Cache Miss Tests

    [Fact]
    public async Task GetProductMemberSchema_WithCacheMissAndValidResult_ShouldCallInnerServiceAndCacheResult()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string schemaFromInnerService = CreateValidSchema();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync((string?)null); // Cache miss

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(schemaFromInnerService);

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().Be(schemaFromInnerService);

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(productId, _cancellationToken),
            Times.Once);

        // Verify result was cached (6 hours TTL)
        _mockCacheProvider.Verify(
            x => x.SetAsync(expectedCacheKey, schemaFromInnerService, TimeSpan.FromHours(6), _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithCacheMissAndNullResult_ShouldCallInnerServiceButNotCacheNull()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync((string?)null); // Cache miss

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync((string?)null); // Inner service returns null

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(productId, _cancellationToken),
            Times.Once);

        // Verify null result was NOT cached
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithCacheMissAndEmptyResult_ShouldCallInnerServiceButNotCacheEmpty()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync((string?)null); // Cache miss

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(string.Empty); // Inner service returns empty string

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().Be(string.Empty);

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(productId, _cancellationToken),
            Times.Once);

        // Verify empty result was NOT cached
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    #endregion

    #region GetProductMemberSchema - Edge Case Tests

    [Fact]
    public async Task GetProductMemberSchema_WithNoTenant_ShouldThrowInvalidOperationException()
    {
        // Arrange
        ProductId productId = CreateProductId();
        SetupTenantProviderFailure(); // No tenant available

        CachedProductService service = CreateService();

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetProductMemberSchema(productId, _cancellationToken));

        exception.Message.Should().Be("Current tenant is not set.");

        // Verify no cache or inner service calls were made
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithCacheFailure_ShouldStillCallInnerServiceAndReturnResult()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string schemaFromInnerService = CreateValidSchema();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);

        // Setup cache to throw exception
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ThrowsAsync(new InvalidOperationException("Cache service unavailable"));

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync(schemaFromInnerService);

        CachedProductService service = CreateService();

        // Act & Assert - Should not throw, should return result from inner service
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().Be(schemaFromInnerService);

        // Verify cache was attempted
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was called as fallback
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(productId, _cancellationToken),
            Times.Once);

        // Cache set should not be called due to cache failure
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithInnerServiceException_ShouldPropagateException()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);
        var expectedException = new InvalidOperationException("Product service unavailable");

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync((string?)null); // Cache miss

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ThrowsAsync(expectedException);

        CachedProductService service = CreateService();

        // Act & Assert
        InvalidOperationException thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetProductMemberSchema(productId, _cancellationToken));

        thrownException.Should().Be(expectedException);

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(productId, _cancellationToken),
            Times.Once);

        // Verify nothing was cached due to exception
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    #endregion

    #region Method Delegation Tests

    [Fact]
    public async Task GetProductPackageType_ShouldAlwaysDelegateToInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();
        string expectedPackageType = "sme";

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync(expectedPackageType);

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductPackageType(productId, _cancellationToken);

        // Assert
        result.Should().Be(expectedPackageType);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);

        // Verify no cache operations were performed
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);

        // Verify no tenant provider calls were made
        _mockTenantProvider.Verify(
            x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny),
            Times.Never);
    }

    [Fact]
    public async Task GetProductPackageType_WithNullResult_ShouldReturnNullFromInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ReturnsAsync((string?)null);

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductPackageType(productId, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetProductPackageType_WithException_ShouldPropagateExceptionFromInnerService()
    {
        // Arrange
        ProductId productId = CreateProductId();
        var expectedException = new InvalidOperationException("Package type service unavailable");

        _mockInnerService
            .Setup(x => x.GetProductPackageType(productId, _cancellationToken))
            .ThrowsAsync(expectedException);

        CachedProductService service = CreateService();

        // Act & Assert
        InvalidOperationException thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetProductPackageType(productId, _cancellationToken));

        thrownException.Should().Be(expectedException);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetProductPackageType(productId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetAvailablePlanIds_ShouldAlwaysDelegateToInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();
        IReadOnlyList<string> expectedPlanIds = new List<string> { "plan1", "plan2", "plan3" };

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ReturnsAsync(expectedPlanIds);

        CachedProductService service = CreateService();

        // Act
        IReadOnlyList<string>? result = await service.GetAvailablePlanIds(domainProductId, _cancellationToken);

        // Assert
        result.Should().BeEquivalentTo(expectedPlanIds);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);

        // Verify no cache operations were performed
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);

        // Verify no tenant provider calls were made
        _mockTenantProvider.Verify(
            x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny),
            Times.Never);
    }

    [Fact]
    public async Task GetAvailablePlanIds_WithNullResult_ShouldReturnNullFromInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ReturnsAsync((IReadOnlyList<string>?)null);

        CachedProductService service = CreateService();

        // Act
        IReadOnlyList<string>? result = await service.GetAvailablePlanIds(domainProductId, _cancellationToken);

        // Assert
        result.Should().BeNull();

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetAvailablePlanIds_WithException_ShouldPropagateExceptionFromInnerService()
    {
        // Arrange
        DomainProductId domainProductId = CreateDomainProductId();
        var expectedException = new InvalidOperationException("Plan IDs service unavailable");

        _mockInnerService
            .Setup(x => x.GetAvailablePlanIds(domainProductId, _cancellationToken))
            .ThrowsAsync(expectedException);

        CachedProductService service = CreateService();

        // Act & Assert
        InvalidOperationException thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.GetAvailablePlanIds(domainProductId, _cancellationToken));

        thrownException.Should().Be(expectedException);

        // Verify inner service was called
        _mockInnerService.Verify(
            x => x.GetAvailablePlanIds(domainProductId, _cancellationToken),
            Times.Once);
    }

    #endregion

    #region Cache Key Generation Tests

    [Fact]
    public async Task GetProductMemberSchema_ShouldGenerateCorrectCacheKeyFormat()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-123");
        var productId = new ProductId
        {
            Type = "health",
            Plan = "basic-plan",
            Version = "v2.1"
        };
        string expectedCacheKey = "schema:product-member:test-tenant-123:health/basic-plan/v2.1";

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync((string?)null);

        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync("test-schema");

        CachedProductService service = CreateService();

        // Act
        await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert - Verify the exact cache key format
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithDifferentTenants_ShouldGenerateDifferentCacheKeys()
    {
        // Arrange
        var tenant1 = new TenantId("tenant-1");
        var tenant2 = new TenantId("tenant-2");
        var productId = new ProductId
        {
            Type = "life",
            Plan = "premium",
            Version = "v1.0"
        };

        string expectedCacheKey1 = "schema:product-member:tenant-1:life/premium/v1.0";
        string expectedCacheKey2 = "schema:product-member:tenant-2:life/premium/v1.0";

        // Setup for first tenant
        SetupTenantProvider(tenant1);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken))
            .ReturnsAsync((string?)null);
        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync("schema-1");

        CachedProductService service1 = CreateService();

        // Act - First tenant
        await service1.GetProductMemberSchema(productId, _cancellationToken);

        // Setup for second tenant (new service instance)
        var mockTenantProvider2 = new Mock<ITenantProvider>();
        mockTenantProvider2
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = tenant2;
                return true;
            });

        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken))
            .ReturnsAsync((string?)null);
        _mockInnerService
            .Setup(x => x.GetProductMemberSchema(productId, _cancellationToken))
            .ReturnsAsync("schema-2");

        var service2 = new CachedProductService(
            mockTenantProvider2.Object,
            _mockInnerService.Object,
            _mockCacheProvider.Object);

        // Act - Second tenant
        await service2.GetProductMemberSchema(productId, _cancellationToken);

        // Assert - Verify different cache keys were used
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken),
            Times.Once);
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithSpecialCharactersInProductId_ShouldHandleCorrectly()
    {
        // Arrange
        var tenantId = new TenantId("tenant-with-dashes");
        var productId = new ProductId
        {
            Type = "health-insurance",
            Plan = "sme-basic_plan",
            Version = "v1.2.3"
        };
        string expectedCacheKey = "schema:product-member:tenant-with-dashes:health-insurance/sme-basic_plan/v1.2.3";

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync("cached-schema");

        CachedProductService service = CreateService();

        // Act
        await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert - Verify cache key handles special characters correctly
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);
    }

    [Theory]
    [InlineData("tenant-1", "health", "basic", "v1.0", "schema:product-member:tenant-1:health/basic/v1.0")]
    [InlineData("tenant-2", "life", "premium", "v2.1", "schema:product-member:tenant-2:life/premium/v2.1")]
    [InlineData("test-tenant", "auto", "comprehensive", "v3.0", "schema:product-member:test-tenant:auto/comprehensive/v3.0")]
    public async Task GetProductMemberSchema_WithVariousInputs_ShouldGenerateExpectedCacheKeys(
        string tenantIdValue, string type, string plan, string version, string expectedCacheKey)
    {
        // Arrange
        var tenantId = new TenantId(tenantIdValue);
        var productId = new ProductId
        {
            Type = type,
            Plan = plan,
            Version = version
        };

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync("test-schema");

        CachedProductService service = CreateService();

        // Act
        await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);
    }

    #endregion

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up any resources if needed
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
