using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;

namespace CoverGo.PoliciesV3.Application.Services;

public class PolicyMemberValidationDataService(
    ILegacyPolicyService legacyPolicyService,
    IProductService productService,
    IMultiTenantFeatureManager featureManager,
    TenantId tenantId,
    IPolicyMemberFieldsSchemaProvider policyMemberFieldsSchemaProvider,
    ILogger<PolicyMemberValidationDataService> logger)
{
    #region Core Member Data Gathering

    /// <summary>
    /// Gathers core member data for validation (individuals, existing members, validation states, dependent members).
    /// This is the shared logic used by both single member and batch member validation.
    /// </summary>
    public async Task<MemberDataResults> GatherMemberDataAsync(
        IEnumerable<string> memberIds,
        PolicyDto policy,
        EndorsementId? endorsementId,
        IUsersService usersService,
        IPolicyMemberQueryService policyMemberQueryService,
        CancellationToken cancellationToken)
    {
        var memberIdsList = memberIds.ToList();
        if (memberIdsList.Count == 0) return MemberDataResults.Empty();

        logger.LogDebug("Gathering member data for {MemberCount} member IDs", memberIdsList.Count);

        // 1. GET INDIVIDUALS AND POLICY MEMBERS
        Task<List<Individual>> getIndividualsTask = usersService.QueryIndividuals(
            new QueryArgumentsOfIndividualWhere
            {
                Where = new IndividualWhere { InternalCode_in = memberIdsList }
            },
            cancellationToken);

        var policyId = (PolicyId)policy.Id;
        var validEndorsementIds = policy.GetValidEndorsementIds(endorsementId?.Value.ToString())
            .Where(id => !string.IsNullOrEmpty(id))
            .Select(id => (EndorsementId)id!)
            .ToList();

        Task<Dictionary<string, PolicyMember?>> getExistingMembersTask = policyMemberQueryService.GetPolicyMembersBatchAsync(
            memberIdsList, policyId, validEndorsementIds, cancellationToken);

        Task<Dictionary<string, List<PolicyMember>>> getMemberValidationStatesTask = policyMemberQueryService.GetMemberValidationStatesBatchAsync(
            memberIdsList, cancellationToken);

        // 2. WAIT FOR ALL OPERATIONS
        await Task.WhenAll(getIndividualsTask, getExistingMembersTask, getMemberValidationStatesTask);

        List<Individual> individuals = await getIndividualsTask;
        Dictionary<string, PolicyMember?> existingMembers = await getExistingMembersTask;
        Dictionary<string, List<PolicyMember>> memberValidationStates = await getMemberValidationStatesTask;

        // 3. CREATE INDIVIDUAL EXISTENCE MAP
        var individualExistenceMap = memberIdsList.ToDictionary(
            memberId => memberId,
            memberId => individuals.Any(ind => string.Equals(ind.InternalCode, memberId, StringComparison.OrdinalIgnoreCase))
        );

        // 4. CONVERT VALIDATION STATES TO READ-ONLY FORMAT
        var readonlyValidationStates = memberValidationStates.ToDictionary(
            kvp => kvp.Key, IReadOnlyList<PolicyMember> (kvp) => kvp.Value
        );

        logger.LogDebug("Gathered member data: {IndividualCount} individuals, {ExistingMemberCount} existing members, {ValidationStateCount} validation states",
            individuals.Count, existingMembers.Count, memberValidationStates.Count);

        return new MemberDataResults(
            individualExistenceMap,
            existingMembers,
            readonlyValidationStates,
            new Dictionary<string, PolicyMember?>()); // Dependent members cache - will be populated by handlers
    }

    /// <summary>
    /// Gathers member-specific validation data including individuals, existing members, and validation states.
    /// </summary>
    public async Task<MemberDataResults> GatherMemberSpecificDataAsync(
        MembersUploadFields memberData,
        PolicyDto policy,
        EndorsementId? endorsementId,
        IUsersService usersService,
        IPolicyMemberQueryService policyMemberQueryService,
        CancellationToken cancellationToken)
    {
        if (memberData.IsEmpty) return MemberDataResults.Empty();

        HashSet<string> allMemberIds = await ExtractAllMemberIdentifiersAsync(memberData, cancellationToken);
        var memberIdsList = allMemberIds.ToList();

        if (memberIdsList.Count == 0) return MemberDataResults.Empty();

        logger.LogDebug("Gathering member-specific data for {MemberCount} unique member IDs", memberIdsList.Count);

        // 1. GET INDIVIDUALS AND POLICY MEMBERS (parallel execution)
        Task<List<Individual>> getIndividualsTask = usersService.QueryIndividuals(
            new QueryArgumentsOfIndividualWhere
            {
                Where = new IndividualWhere { InternalCode_in = memberIdsList }
            },
            cancellationToken);

        var policyId = (PolicyId)policy.Id;
        var validEndorsementIds = policy.GetValidEndorsementIds(endorsementId?.Value.ToString())
            .Where(id => !string.IsNullOrEmpty(id))
            .Select(id => (EndorsementId)id!)
            .ToList();

        Task<Dictionary<string, PolicyMember?>> getExistingMembersTask = policyMemberQueryService.GetPolicyMembersBatchAsync(
            memberIdsList, policyId, validEndorsementIds, cancellationToken);

        Task<Dictionary<string, List<PolicyMember>>> getMemberValidationStatesTask = policyMemberQueryService.GetMemberValidationStatesBatchAsync(
            memberIdsList, cancellationToken);

        // 2. GET DEPENDENT MEMBER CACHE
        HashSet<string> primaryMemberIds = await ExtractPrimaryMemberIdentifiersAsync(memberData, cancellationToken);
        Dictionary<string, PolicyMember?> dependentMembersCache = await GatherDependentMembersCacheAsync(
            primaryMemberIds, policy, endorsementId, policyMemberQueryService, cancellationToken);

        // 3. CREATE FINAL MEMBER DATA RESULTS WITH DEPENDENT CACHE
        await Task.WhenAll(getIndividualsTask, getExistingMembersTask, getMemberValidationStatesTask);

        List<Individual> individuals = await getIndividualsTask;
        Dictionary<string, PolicyMember?> existingMembers = await getExistingMembersTask;
        Dictionary<string, List<PolicyMember>> memberValidationStates = await getMemberValidationStatesTask;

        var individualExistenceMap = allMemberIds.ToDictionary(
            memberId => memberId,
            memberId => individuals.Any(ind => string.Equals(ind.InternalCode, memberId, StringComparison.OrdinalIgnoreCase))
        );

        var readonlyValidationStates = memberValidationStates.ToDictionary(
            kvp => kvp.Key,
            kvp => (IReadOnlyList<PolicyMember>)kvp.Value
        );

        logger.LogDebug("Gathered member data: {IndividualCount} individuals, {ExistingMemberCount} existing members, {ValidationStateCount} validation states",
            individuals.Count, existingMembers.Count, memberValidationStates.Count);

        return new MemberDataResults(
            individualExistenceMap,
            existingMembers,
            readonlyValidationStates,
            dependentMembersCache);
    }

    /// <summary>
    /// Gathers dependent members cache for the given primary member IDs.
    /// Handlers use this to populate the dependent members cache in MemberDataResults.
    /// </summary>
    public static async Task<Dictionary<string, PolicyMember?>> GatherDependentMembersCacheAsync(
        IEnumerable<string> primaryMemberIds,
        PolicyDto policy,
        EndorsementId? endorsementId,
        IPolicyMemberQueryService policyMemberQueryService,
        CancellationToken cancellationToken)
    {
        var primaryMemberIdsList = primaryMemberIds.ToList();
        if (primaryMemberIdsList.Count == 0) return new Dictionary<string, PolicyMember?>();

        var policyId = (PolicyId)policy.Id;
        var validEndorsementIds = policy.GetValidEndorsementIds(endorsementId?.Value.ToString())
            .Where(id => !string.IsNullOrEmpty(id))
            .Select(id => (EndorsementId)id!)
            .ToList();

        return await policyMemberQueryService.GetPolicyMembersBatchAsync(
            primaryMemberIdsList, policyId, validEndorsementIds, cancellationToken);
    }

    #endregion

    #region Member Information Extraction (Batch Processing)

    /// <summary>
    /// Processes member data in batches for efficient memory management and async processing.
    /// </summary>
    /// <param name="memberData">The member data to process in batches</param>
    /// <param name="batchSize">Size of each batch (default: from ValidationConstants)</param>
    /// <param name="cancellationToken">Cancellation token for cooperative cancellation</param>
    /// <returns>Async enumerable of batches with their starting indices</returns>
    private static async IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> ProcessMembersInBatches(
        MembersUploadFields memberData,
        int batchSize = ValidationConstants.BatchProcessing.DefaultMemberBatchSize,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        for (int i = 0; i < memberData.Count; i += batchSize)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int endIndex = Math.Min(i + batchSize, memberData.Count);
            var batch = new List<MemberUploadFields>(endIndex - i);

            for (int j = i; j < endIndex; j++) batch.Add(memberData[j]);

            yield return (batch, i);

            await Task.Yield();
        }
    }

    /// <summary>
    /// Processes batches of member data to extract unique member IDs efficiently.
    /// </summary>
    /// <param name="batches">Async enumerable of member data batches</param>
    /// <param name="cancellationToken">Cancellation token for the async operation</param>
    /// <returns>HashSet of unique member identifiers</returns>
    private async Task<HashSet<string>> ValidateBatchAsync(
        IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> batches,
        CancellationToken cancellationToken = default)
    {
        var batchList = new List<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)>();

        // Collect all batches first
        await foreach ((IReadOnlyList<MemberUploadFields> batch, int startIndex) in batches.WithCancellation(cancellationToken)) batchList.Add((batch, startIndex));

        if (batchList.Count == 0) return new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        logger.LogDebug("Processing {BatchCount} batches in parallel for member ID extraction with max degree of parallelism {MaxDegreeOfParallelism}",
            batchList.Count, ValidationConstants.Concurrency.DefaultMaxConcurrentValidations);

        var concurrentResults = new ConcurrentDictionary<int, HashSet<string>>();
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = ValidationConstants.Concurrency.DefaultMaxConcurrentValidations,
            CancellationToken = cancellationToken
        };

        Parallel.ForEach(
            batchList.Select((batch, index) => new { batch, index }),
            parallelOptions,
            item =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                (IReadOnlyList<MemberUploadFields> batch, int _) = item.batch;
                HashSet<string> batchResult = ProcessMemberIdBatchSync(batch, cancellationToken);
                concurrentResults[item.index] = batchResult;
            });

        var memberIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        foreach (HashSet<string> batchResult in concurrentResults.OrderBy(x => x.Key).Select(kvp => kvp.Value))
            memberIds.UnionWith(batchResult);

        logger.LogDebug("Parallel batch processing completed: {BatchCount} batches processed, {UniqueIds} unique member IDs extracted",
            batchList.Count, memberIds.Count);

        return memberIds;
    }

    /// <summary>
    /// Processes a single batch to extract member identifiers synchronously.
    /// </summary>
    private static HashSet<string> ProcessMemberIdBatchSync(
        IReadOnlyList<MemberUploadFields> batch,
        CancellationToken cancellationToken = default)
    {
        var memberIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        foreach (MemberUploadFields memberFields in batch)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);

            if (!string.IsNullOrWhiteSpace(memberId)) memberIds.Add(memberId);
        }

        return memberIds;
    }

    /// <summary>
    /// Builds a memory-efficient global primary member index using an array instead of Dictionary.
    /// This reduces memory overhead by ~50% compared to Dictionary while maintaining O(1) lookup.
    /// </summary>
    /// <param name="originalMemberData">The complete member data</param>
    /// <returns>Array mapping each member index to their primary member index (-1 if no primary)</returns>
    private static int[] BuildGlobalPrimaryMemberIndex(MembersUploadFields originalMemberData)
    {
        var globalPrimaryMemberIndex = new int[originalMemberData.Count];
        int lastPrimaryMemberIndex = -1;

        // Single O(n) pass to build the global index
        for (int i = 0; i < originalMemberData.Count; i++)
        {
            MemberUploadFields member = originalMemberData[i];

            if (!member.IsDependent())
            {
                // This is a primary member - update the last known primary
                lastPrimaryMemberIndex = i;
                globalPrimaryMemberIndex[i] = -1; // Primary members don't have a primary member
            }
            else
            {
                // This is a dependent - assign the last known primary member
                globalPrimaryMemberIndex[i] = lastPrimaryMemberIndex;
            }
        }

        return globalPrimaryMemberIndex;
    }

    /// <summary>
    /// Processes batches to find primary members that have dependents using a global index for optimal memory usage.
    /// </summary>
    /// <param name="batches">Async enumerable of member data batches with indices</param>
    /// <param name="originalMemberData">Original member data for dependent relationship lookup</param>
    /// <param name="cancellationToken">Cancellation token for the async operation</param>
    /// <returns>HashSet of unique primary member identifiers</returns>
    private static async Task<HashSet<string>> ValidatePrimaryMemberBatchAsync(
        IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> batches,
        MembersUploadFields originalMemberData,
        CancellationToken cancellationToken = default)
    {
        var batchList = new List<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)>();

        // Collect all batches first
        await foreach ((IReadOnlyList<MemberUploadFields> batch, int startIndex) in batches.WithCancellation(cancellationToken))
            batchList.Add((batch, startIndex));

        if (batchList.Count == 0) return new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        // Build global primary member index once for all batches - significant memory savings
        int[] globalPrimaryMemberIndex = BuildGlobalPrimaryMemberIndex(originalMemberData);

        var concurrentResults = new ConcurrentDictionary<int, HashSet<string>>();
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = ValidationConstants.Concurrency.DefaultMaxConcurrentValidations,
            CancellationToken = cancellationToken
        };

        Parallel.ForEach(
            batchList.Select((batch, index) => new { batch, index }),
            parallelOptions,
            item =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                (IReadOnlyList<MemberUploadFields> batch, int startIndex) = item.batch;
                HashSet<string> batchResult = ProcessPrimaryMemberBatchSyncWithGlobalIndex(
                    batch, startIndex, originalMemberData, globalPrimaryMemberIndex, cancellationToken);
                concurrentResults[item.index] = batchResult;
            });

        // Merge all results into a single HashSet
        var primaryMemberIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        foreach (HashSet<string> batchResult in concurrentResults.OrderBy(x => x.Key).Select(kvp => kvp.Value))
            primaryMemberIds.UnionWith(batchResult);

        return primaryMemberIds;
    }

    /// <summary>
    /// Processes a single batch to extract primary member identifiers for dependents using a global array index.
    /// Optimized for memory efficiency by using an array instead of Dictionary and reusing across all batches.
    /// </summary>
    private static HashSet<string> ProcessPrimaryMemberBatchSyncWithGlobalIndex(
        IReadOnlyList<MemberUploadFields> batch,
        int startIndex,
        MembersUploadFields originalMemberData,
        int[] globalPrimaryMemberIndex,
        CancellationToken cancellationToken = default)
    {
        var primaryMemberIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        for (int i = 0; i < batch.Count; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();

            MemberUploadFields memberFields = batch[i];
            if (!memberFields.IsDependent()) continue;

            int globalIndex = startIndex + i;

            // Use O(1) array lookup instead of O(k) backward search - more memory efficient than Dictionary
            if (globalIndex < globalPrimaryMemberIndex.Length)
            {
                int primaryMemberGlobalIndex = globalPrimaryMemberIndex[globalIndex];
                if (primaryMemberGlobalIndex >= 0)
                {
                    MemberUploadFields primaryMember = originalMemberData[primaryMemberGlobalIndex];
                    string? primaryMemberId = primaryMember.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);

                    if (!string.IsNullOrWhiteSpace(primaryMemberId))
                        primaryMemberIds.Add(primaryMemberId);
                }
            }
        }

        return primaryMemberIds;
    }

    /// <summary>
    /// Extracts all unique member identifiers from the upload data using async batch processing.
    /// </summary>
    public async Task<HashSet<string>> ExtractAllMemberIdentifiersAsync(
        MembersUploadFields memberData,
        CancellationToken cancellationToken = default)
    {
        if (memberData.IsEmpty) return new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        logger.LogDebug("Starting parallel extraction of member identifiers for {MemberCount} members", memberData.Count);

        IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> batches =
            ProcessMembersInBatches(memberData, ValidationConstants.BatchProcessing.DefaultMemberBatchSize, cancellationToken);

        HashSet<string> result = await ValidateBatchAsync(batches, cancellationToken);

        logger.LogInformation("Completed parallel member identifier extraction: {MemberCount} members processed, {UniqueIds} unique IDs found",
            memberData.Count, result.Count);

        return result;
    }

    /// <summary>
    /// Extracts identifiers of primary members that have dependents using async batch processing.
    /// Used for validating dependent member relationships.
    /// </summary>
    public async Task<HashSet<string>> ExtractPrimaryMemberIdentifiersAsync(
        MembersUploadFields memberData,
        CancellationToken cancellationToken = default)
    {
        if (memberData.IsEmpty) return new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        logger.LogDebug("Starting parallel extraction of primary member identifiers for {MemberCount} members", memberData.Count);

        IAsyncEnumerable<(IReadOnlyList<MemberUploadFields> Batch, int StartIndex)> batches =
            ProcessMembersInBatches(memberData, ValidationConstants.BatchProcessing.DefaultMemberBatchSize, cancellationToken);

        HashSet<string> result = await ValidatePrimaryMemberBatchAsync(batches, memberData, cancellationToken);

        logger.LogInformation("Completed parallel primary member identifier extraction: {MemberCount} members processed, {PrimaryIds} primary IDs found",
            memberData.Count, result.Count);

        return result;
    }

    #endregion

    #region Feature Flags and Product Data

    /// <summary>
    /// Gathers feature flags and product data for validation.
    /// </summary>
    public async Task<(bool[] FeatureFlags, IReadOnlyList<string>? AvailablePlans, string? PackageType, List<string>? ContractHolderPolicies)> GatherFeatureFlagsAndProductDataAsync(
        PolicyDto policy,
        CancellationToken cancellationToken)
    {
        // 1. GATHER FEATURE FLAGS AND PRODUCT DATA (parallel execution)
        Task<bool>[] featureCheckTasks =
        [
            featureManager.IsEnabled("UseTheSamePlanForEmployeeAndDependents", tenantId.Value),
            featureManager.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", tenantId.Value),
            featureManager.IsEnabled("AllowMembersFromOtherContractHolders", tenantId.Value)
        ];

        Task<IReadOnlyList<string>?> getAvailablePlansTask = GetAvailablePlansAsync(policy, cancellationToken);
        Task<string?> getPackageTypeTask = GetPackageTypeAsync(policy, cancellationToken);
        Task<List<string>> getContractHolderPoliciesTask = legacyPolicyService.GetIdsByContractHolderId(policy.ContractHolderId, cancellationToken);

        // 2. WAIT FOR ALL PARALLEL OPERATIONS
        await Task.WhenAll(
            Task.WhenAll(featureCheckTasks),
            getAvailablePlansTask,
            getPackageTypeTask,
            getContractHolderPoliciesTask);

        bool[] featureFlags = await Task.WhenAll(featureCheckTasks);
        IReadOnlyList<string>? availablePlans = await getAvailablePlansTask;
        string? packageType = await getPackageTypeTask;
        List<string> contractHolderPolicies = await getContractHolderPoliciesTask;

        return (featureFlags, availablePlans, packageType, contractHolderPolicies);
    }

    /// <summary>
    /// Gets available plans for the policy's product, or null if no ProductId.
    /// </summary>
    public async Task<IReadOnlyList<string>?> GetAvailablePlansAsync(PolicyDto policy, CancellationToken cancellationToken)
    {
        if (policy.ProductId == null)
        {
            logger.LogDebug("Policy {PolicyId} has no ProductId, skipping available plans lookup", policy.Id);
            return null;
        }

        ProductId domainProductId = policy.ProductId.ToDomainProductId();
        return await productService.GetAvailablePlanIds(domainProductId, cancellationToken);
    }

    /// <summary>
    /// Gets package type for the policy's product, or null if no ProductId.
    /// </summary>
    public async Task<string?> GetPackageTypeAsync(PolicyDto policy, CancellationToken cancellationToken)
    {
        if (policy.ProductId == null)
        {
            logger.LogDebug("Policy {PolicyId} has no ProductId, skipping package type lookup", policy.Id);
            return null;
        }

        ProductId domainProductId = policy.ProductId.ToDomainProductId();
        var clientProductId = new Products.Client.ProductId
        {
            Plan = domainProductId.Plan,
            Type = domainProductId.Type,
            Version = domainProductId.Version
        };

        return await productService.GetProductPackageType(clientProductId, cancellationToken);
    }

    /// <summary>
    /// Gets the data schema that defines the structure of the upload file.
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetDataSchemaForUploadAsync(
        PolicyDto policy,
        EndorsementId? endorsementId,
        CancellationToken cancellationToken)
    {
        if (policy.ProductId == null)
        {
            logger.LogWarning("Policy {PolicyId} is missing ProductId, returning empty schema", policy.Id);
            return new PolicyMemberFieldsSchema([]);
        }

        logger.LogDebug("Fetching schema for policy {PolicyId}", policy.Id);
        PolicyMemberFieldsSchema schema = await policyMemberFieldsSchemaProvider.GetMemberUploadSchema(
            policy.ContractHolderId,
            policy.ProductId.ToDomainProductId(),
            endorsementId,
            cancellationToken) ?? throw new BadSchemaConfigException($"Could not retrieve upload schema for policy {policy.Id} with product {policy.ProductId}");

        return schema;
    }

    /// <summary>
    /// Gets contract holder scope endorsements for validation.
    /// </summary>
    public async Task<List<EndorsementId>> GetContractHolderScopeEndorsementsAsync(
        List<string> contractHolderPolicyIds,
        bool isPolicyV2,
        CancellationToken cancellationToken)
    {
        if (contractHolderPolicyIds.Count == 0)
            return [];

        try
        {
            List<PolicyDto> contractHolderPolicies = await legacyPolicyService.GetPolicyDtosByIds(
                contractHolderPolicyIds, cancellationToken);

            logger.LogDebug("Retrieved {RetrievedCount} out of {RequestedCount} contract holder policies",
                contractHolderPolicies.Count, contractHolderPolicyIds.Count);

            if (contractHolderPolicies.Count == 0)
            {
                logger.LogDebug("No contract holder policies found, returning empty endorsement list");
                return [];
            }

            // Apply business rule filtering
            contractHolderPolicies = PolicyDto.FilterForContractHolderScopeValidation(contractHolderPolicies, isPolicyV2);

            // Get approved endorsements from all filtered policies
            List<string?> endorsementIdStrings = PolicyDto.GetApprovedEndorsementIdsForPolicies(contractHolderPolicies);

            var endorsementIds = endorsementIdStrings
                .Where(id => id != null && Guid.TryParse(input: id, out _))
                .Select(id => (EndorsementId)id!)
                .ToList();

            return endorsementIds;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting contract holder scope endorsements");
            return [];
        }
    }

    #endregion

    #region Validation Data Creation

    /// <summary>
    /// Creates ResolvedValidationData from gathered information.
    /// </summary>
    public ResolvedValidationData CreateResolvedValidationData(
        PolicyDto policy,
        bool[] featureFlags,
        IReadOnlyList<string>? availablePlans,
        string? packageType,
        List<string>? contractHolderPolicies,
        List<EndorsementId> contractHolderScopeEndorsements,
        MemberDataResults memberDataResults,
        List<string> validEndorsementIds,
        Dictionary<string, PolicyMember?> dependentMembersCache) =>
        new()
        {
            UseTheSamePlanForEmployeeAndDependents = featureFlags[0],
            OnlyApplyForSmeProducts = featureFlags[1],
            AllowMembersFromOtherContractHolders = featureFlags[2],
            AvailablePlans = availablePlans?.ToHashSet(),
            IsProductSme = string.Equals(packageType, "sme", StringComparison.OrdinalIgnoreCase),
            ContractHolderPolicyIds = contractHolderPolicies ?? [],
            ValidEndorsementIds = validEndorsementIds,
            ContractHolderScopeEndorsements = contractHolderScopeEndorsements,
            IsPolicyV2 = policy.IsV2,
            TenantId = tenantId.Value,
            DependentMembersCache = dependentMembersCache,
            ExistingIndividualIds = new HashSet<string>(memberDataResults.IndividualExistenceMap.Keys),
            ExistingPolicyMembers = memberDataResults.ExistingPolicyMembers,
            MemberValidationStates = memberDataResults.MemberValidationStates,
            IndividualExistenceMap = memberDataResults.IndividualExistenceMap
        };

    #endregion

    #region Policy Validation

    ///<summary>
    /// Validates that the policy can accept member additions.
    /// </summary>
    public void ValidatePolicyState(PolicyDto policy, EndorsementId? endorsementId)
    {
        try
        {
            policy.ValidatePolicyStateForMemberUpload(endorsementId?.Value);
        }
        catch (PolicyProductIdMissingException)
        {
            logger.LogWarning("Policy {PolicyId} is missing ProductId, proceeding with limited validation", policy.Id);
        }
    }

    #endregion
}